import delay from 'delay';

import fetch from 'isomorphic-fetch';

import * as fs from 'node:fs';
import * as os from 'node:os';
import * as path from 'node:path';

import * as vscode from 'vscode';

import { ChatGPTAPI as ChatGPTAPI3 } from '../chatgpt-4.7.2/index';
import { ChatGPTAPI as ChatGPTAPI35 } from '../chatgpt-5.1.1/index';

import { AdvancedAIEngine } from "./advanced-ai-engine";
import { ASTAnalyzer } from "./ast-analyzer";
import { CollaborationEngine } from "./collaboration-engine";
import { ComponentLibraryManager } from "./component-library";
import { EnterpriseManager } from "./enterprise-features";
import { EnhancedFileManager } from "./file-manager";
import { FileWatcher } from "./file-watcher";
import { LivePreviewManager } from "./live-preview";
import { ProjectScaffolder } from "./project-scaffolder";
import { AuthType, LoginMethod } from "./types";
import { UIPromptTemplates, UIPromptContext } from "./ui-prompts";
import { VectorDatabase } from "./vector-database";

export default class UIOrbitViewProvider implements vscode.WebviewViewProvider {
	private webView?: vscode.WebviewView;

	public subscribeToResponse: boolean;
	public autoScroll: boolean;
	public useGpt3?: boolean;
	public model?: string;
	public framework?: string;
	public stylingFramework?: string;
	public designSystem?: string;
	public accessibilityLevel?: string;

	// UIOrbit-specific services
	private vectorDb: VectorDatabase;
	private astAnalyzer: ASTAnalyzer;
	private fileWatcher: FileWatcher;
	private projectScaffolder: ProjectScaffolder;
	private livePreview: LivePreviewManager;
	private componentLibrary: ComponentLibraryManager;
	private fileManager: EnhancedFileManager;
	private aiEngine: AdvancedAIEngine;
	private collaborationEngine: CollaborationEngine;
	private enterpriseManager: EnterpriseManager;

	private apiGpt3?: ChatGPTAPI3;
	private apiGpt35?: ChatGPTAPI35;
	private conversationId?: string;
	private messageId?: string;
	private proxyServer?: string;
	private loginMethod?: LoginMethod;
	private authType?: AuthType;

	private questionCounter: number = 0;
	private inProgress: boolean = false;
	private abortController?: AbortController;
	private currentMessageId: string = "";
	private response: string = "";

	/**
	 * Message to be rendered lazily if they haven't been rendered
	 * in time before resolveWebviewView is called.
	 */
	private leftOverMessage?: any;
	constructor(private context: vscode.ExtensionContext) {
		this.subscribeToResponse = vscode.workspace.getConfiguration("uiorbit").get("response.showNotification") || false;
		this.autoScroll = !!vscode.workspace.getConfiguration("uiorbit").get("response.autoScroll");
		this.model = vscode.workspace.getConfiguration("uiorbit").get("model") as string;
		this.framework = vscode.workspace.getConfiguration("uiorbit").get("framework") as string;
		this.stylingFramework = vscode.workspace.getConfiguration("uiorbit").get("stylingFramework") as string;
		this.designSystem = vscode.workspace.getConfiguration("uiorbit").get("designSystem") as string;
		this.accessibilityLevel = vscode.workspace.getConfiguration("uiorbit").get("accessibilityLevel") as string;

		// Initialize UIOrbit services
		this.vectorDb = new VectorDatabase(context);
		this.astAnalyzer = new ASTAnalyzer();
		this.fileWatcher = new FileWatcher(context, this.vectorDb);

		// Initialize enterprise features
		const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
		this.projectScaffolder = new ProjectScaffolder(workspaceRoot);
		this.livePreview = new LivePreviewManager(workspaceRoot);
		this.componentLibrary = new ComponentLibraryManager(workspaceRoot);
		this.fileManager = new EnhancedFileManager(workspaceRoot);
		this.aiEngine = new AdvancedAIEngine(workspaceRoot);
		this.collaborationEngine = new CollaborationEngine(workspaceRoot);
		this.enterpriseManager = new EnterpriseManager(workspaceRoot);

		// Initialize enterprise features
		this.enterpriseManager.initializeEnterprise();

		this.setMethod();
	}

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		_context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		this.webView = webviewView;

		webviewView.webview.options = {
			// Allow scripts in the webview
			enableScripts: true,

			localResourceRoots: [
				this.context.extensionUri
			]
		};

		webviewView.webview.html = this.getWebviewHtml(webviewView.webview);

		webviewView.webview.onDidReceiveMessage(async data => {
			switch (data.type) {
				case 'addFreeTextQuestion':
					this.sendApiRequest(data.value, { command: "freeText" });
					break;
				case 'editCode':
					const escapedString = (data.value as string).replace(/\$/g, '\\$');;
					vscode.window.activeTextEditor?.insertSnippet(new vscode.SnippetString(escapedString));

					this.logEvent("code-inserted");
					break;
				case 'openNew':
					const document = await vscode.workspace.openTextDocument({
						content: data.value,
						language: data.language
					});
					vscode.window.showTextDocument(document);

					this.logEvent(data.language === "markdown" ? "code-exported" : "code-opened");
					break;
				case 'clearConversation':
					this.messageId = undefined;
					this.conversationId = undefined;

					this.logEvent("conversation-cleared");
					break;
				case 'clearBrowser':
					this.logEvent("browser-cleared");
					break;
				case 'cleargpt3':
					this.apiGpt3 = undefined;

					this.logEvent("gpt3-cleared");
					break;
				case 'login':
					this.prepareConversation().then(success => {
						if (success) {
							this.sendMessage({ type: 'loginSuccessful', showConversations: false }, true);

							this.logEvent("logged-in");
						}
					});
					break;
				case 'openSettings':
					vscode.commands.executeCommand('workbench.action.openSettings', "@ext:UIOrbit.uiorbit-vscode uiorbit.");

					this.logEvent("settings-opened");
					break;
				case 'openSettingsPrompt':
					vscode.commands.executeCommand('workbench.action.openSettings', "@ext:UIOrbit.uiorbit-vscode promptPrefix");

					this.logEvent("settings-prompt-opened");
					break;
				case 'listConversations':
					this.logEvent("conversations-list-attempted");
					break;
				case 'showConversation':
					/// ...
					break;
				case "stopGenerating":
					this.stopGenerating();
					break;
				default:
					break;
			}
		});

		if (this.leftOverMessage !== null) {
			// If there were any messages that wasn't delivered, render after resolveWebView is called.
			this.sendMessage(this.leftOverMessage);
			this.leftOverMessage = null;
		}
	}

	private stopGenerating(): void {
		this.abortController?.abort?.();
		this.inProgress = false;
		this.sendMessage({ type: 'showInProgress', inProgress: this.inProgress });
		const responseInMarkdown = !this.isCodexModel;
		this.sendMessage({ type: 'addResponse', value: this.response, done: true, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });
		this.logEvent("stopped-generating");
	}

	public clearSession(): void {
		this.stopGenerating();
		this.apiGpt3 = undefined;
		this.messageId = undefined;
		this.conversationId = undefined;
		this.logEvent("cleared-session");
	}

	public setMethod(): void {
		this.loginMethod = vscode.workspace.getConfiguration("uiorbit").get("method") as LoginMethod;
		this.useGpt3 = true;
		this.clearSession();
	}

	public setUIFramework(): void {
		this.framework = vscode.workspace.getConfiguration("uiorbit").get("framework");
		this.stylingFramework = vscode.workspace.getConfiguration("uiorbit").get("stylingFramework");
		this.designSystem = vscode.workspace.getConfiguration("uiorbit").get("designSystem");
		this.accessibilityLevel = vscode.workspace.getConfiguration("uiorbit").get("accessibilityLevel");
	}

	private get isCodexModel(): boolean {
		return !!this.model?.startsWith("code-");
	}

	private get isGpt35Model(): boolean {
		return !!this.model?.startsWith("gpt-");
	}

	public async prepareConversation(modelChanged = false): Promise<boolean> {
		if (modelChanged) {
			// Model changed, need to reinitialize
			// Continue with reinitialization
		}

		const state = this.context.globalState;
		const configuration = vscode.workspace.getConfiguration("uiorbit");

		if (this.useGpt3) {
			if ((this.isGpt35Model && !this.apiGpt35) || (!this.isGpt35Model && !this.apiGpt3) || modelChanged) {
				let apiKey = configuration.get("apiKey") as string || state.get("uiorbit-api-key") as string;
				const organization = configuration.get("organization") as string;
				const max_tokens = configuration.get("maxTokens") as number;
				const temperature = configuration.get("temperature") as number;
				const apiBaseUrl = configuration.get("apiBaseUrl") as string;

				if (!apiKey) {
					vscode.window.showErrorMessage("Please add your OpenAI API Key to use UIOrbit. You can store it in settings or enter it temporarily for this session.", "Store in session (Recommended)", "Open settings").then(async choice => {
						if (choice === "Open settings") {
							vscode.commands.executeCommand('workbench.action.openSettings', "uiorbit.apiKey");
							return false;
						} else if (choice === "Store in session (Recommended)") {
							await vscode.window
								.showInputBox({
									title: "Store OpenAI API Key in session",
									prompt: "Please enter your OpenAI API Key to store in your session only. This option won't persist the token on your settings.json file. You may need to re-enter after restarting your VS-Code",
									ignoreFocusOut: true,
									placeHolder: "API Key",
									value: apiKey || ""
								})
								.then((value) => {
									if (value) {
										apiKey = value;
										state.update("uiorbit-api-key", apiKey);
										this.sendMessage({ type: 'loginSuccessful', showConversations: false }, true);
									}
								});
						}
					});

					return false;
				}

				if (this.isGpt35Model) {
					this.apiGpt35 = new ChatGPTAPI35({
						apiKey,
						fetch: fetch,
						apiBaseUrl: apiBaseUrl?.trim() || undefined,
						organization,
						completionParams: {
							model: this.model,
							max_tokens,
							temperature,
						}
					});
				} else {
					this.apiGpt3 = new ChatGPTAPI3({
						apiKey,
						fetch: fetch,
						apiBaseUrl: apiBaseUrl?.trim() || undefined,
						organization,
						completionParams: {
							model: this.model,
							max_tokens,
							temperature,
						}
					});
				}
			}
		}

		this.sendMessage({ type: 'loginSuccessful', showConversations: false }, true);

		return true;
	}

	private get systemContext() {
		return `You are ChatGPT helping the User with coding. 
			You are intelligent, helpful and an expert developer, who always gives the correct answer and only does what instructed. You always answer truthfully and don't make things up. 
			(When responding to the following prompt, please make sure to properly style your response using Github Flavored Markdown. 
			Use markdown syntax for things like headings, lists, colored text, code blocks, highlights etc. Make sure not to mention markdown or styling in your actual response.)`;
	}

	private processQuestion(question: string, code?: string, language?: string) {
		// Add UI/UX context to the prompt
		let uiContext = this.buildUIContext();

		if (code != null) {
			// Add prompt prefix to the code if there was a code block selected
			question = `${uiContext}\n\n${question}${language ? ` (The following code is in ${language} programming language)` : ''}: ${code}`;
		} else {
			question = `${uiContext}\n\n${question}`;
		}
		return question + "\r\n";
	}

	private buildUIContext(): string {
		const framework = this.framework || 'Auto-detect';
		const styling = this.stylingFramework || 'Auto-detect';
		const designSystem = this.designSystem || 'None';
		const accessibility = this.accessibilityLevel || 'WCAG AA';

		return `You are UIOrbit, an expert UI/UX developer assistant specializing in modern frontend development.

Current Project Context:
- Framework: ${framework}
- Styling: ${styling}
- Design System: ${designSystem}
- Accessibility Level: ${accessibility}

Guidelines:
- Generate modern, responsive, and accessible UI components
- Follow current UI/UX best practices and trends
- Use semantic HTML and proper ARIA attributes
- Ensure mobile-first responsive design
- Apply the specified design system principles when applicable
- Write clean, maintainable code with proper component structure
- Include helpful comments explaining design decisions`;
	}

	private async processUIQuestion(prompt: string, options: { command: string, code?: string, language?: string }): Promise<string> {
		const context: UIPromptContext = {
			framework: this.framework,
			stylingFramework: this.stylingFramework,
			designSystem: this.designSystem,
			accessibilityLevel: this.accessibilityLevel,
			language: options.language
		};

		// Get project context for enhanced suggestions
		const projectContext = await this.getEnhancedProjectContext();
		let enhancedPrompt = prompt;

		// Use specific UI prompts based on command with AI enhancement
		switch (options.command) {
			case 'generateComponent':
				const codeContext = await this.aiEngine.buildCodeContext();
				enhancedPrompt = await this.aiEngine.generateContextAwareComponent(prompt, codeContext);
				break;
			case 'addStyling':
				enhancedPrompt = UIPromptTemplates.addStyling(context);
				break;
			case 'makeResponsive':
				enhancedPrompt = UIPromptTemplates.makeResponsive(context);
				break;
			case 'addAccessibility':
				enhancedPrompt = UIPromptTemplates.addAccessibility(context);
				break;
			case 'optimizeUI':
				enhancedPrompt = UIPromptTemplates.optimizeUI(context);
				break;
			case 'explainDesign':
				enhancedPrompt = UIPromptTemplates.explainDesign(context);
				break;
			case 'generateVariants':
				enhancedPrompt = UIPromptTemplates.generateVariants(context);
				break;
			case 'trendingPatterns':
				enhancedPrompt = UIPromptTemplates.trendingPatterns(context);
				break;
			case 'designSystem':
				enhancedPrompt = UIPromptTemplates.designSystem(context);
				break;
			case 'customUIPrompt':
				enhancedPrompt = UIPromptTemplates.customUIPrompt(context, prompt);
				break;
			default:
				// For freeText and other commands, use the original prompt with UI context
				enhancedPrompt = this.buildUIContext() + '\n\n' + prompt;
				break;
		}

		// Add project context for better suggestions
		if (projectContext) {
			enhancedPrompt += `\n\nProject Context:\n${projectContext}`;
		}

		if (options.code) {
			enhancedPrompt += `${options.language ? ` (The following code is in ${options.language} programming language)` : ''}: ${options.code}`;
		}

		return enhancedPrompt + "\r\n";
	}

	private async getEnhancedProjectContext(): Promise<string> {
		try {
			const fileContext = await this.fileWatcher.getContextForCurrentFile();
			const projectSummary = await this.fileWatcher.getProjectSummary();

			let contextInfo = `Project Summary:
- Total Files: ${projectSummary.totalFiles}
- Components: ${projectSummary.components}
- Frameworks: ${projectSummary.frameworks.join(', ')}
- Recent Changes: ${projectSummary.recentChanges}`;

			if (fileContext.currentFile) {
				contextInfo += `\n\nCurrent File Context:
- Framework: ${fileContext.currentFile.framework}
- Dependencies: ${fileContext.currentFile.dependencies.join(', ')}
- Components: ${fileContext.currentFile.components.join(', ')}`;
			}

			if (fileContext.suggestions.length > 0) {
				contextInfo += `\n\nSuggestions:
${fileContext.suggestions.map(s => `- ${s}`).join('\n')}`;
			}

			return contextInfo;
		} catch (error) {
			console.error('Error getting project context:', error);
			return '';
		}
	}

	public async sendApiRequest(prompt: string, options: { command: string, code?: string, previousAnswer?: string, language?: string; }) {
		if (this.inProgress) {
			// The AI is still thinking... Do not accept more questions.
			return;
		}

		this.questionCounter++;

		this.logEvent("api-request-sent", { "uiorbit.command": options.command, "uiorbit.hasCode": String(!!options.code), "uiorbit.hasPreviousAnswer": String(!!options.previousAnswer) });

		// Check if we need to scaffold a new project
		if (await this.projectScaffolder.needsScaffolding() && this.isComponentGenerationRequest(prompt)) {
			const shouldScaffold = await vscode.window.showInformationMessage(
				'🚀 No project detected. Would you like UIOrbit to create a new project for you?',
				'Yes, create project',
				'No, continue anyway'
			);

			if (shouldScaffold === 'Yes, create project') {
				const recommendations = this.projectScaffolder.getProjectRecommendations(prompt);
				this.sendMessage({
					type: 'addResponse',
					value: recommendations,
					autoScroll: this.autoScroll
				});

				const shouldProceed = await vscode.window.showInformationMessage(
					'Create this project setup?',
					'Yes, create it',
					'No, let me choose'
				);

				if (shouldProceed === 'Yes, create it') {
					const config = this.projectScaffolder.detectFrameworkFromPrompt(prompt);
					const success = await this.projectScaffolder.createProject(config);
					if (success) {
						return; // Project created, user will be redirected
					}
				}
			}
		}

		if (!await this.prepareConversation()) {
			return;
		}

		this.response = '';
		let question = await this.processUIQuestion(prompt, options);
		const responseInMarkdown = !this.isCodexModel;

		// If the UIOrbit view is not in focus/visible; focus on it to render Q&A
		if (this.webView == null) {
			vscode.commands.executeCommand('uiorbit.view.focus');
		} else {
			this.webView?.show?.(true);
		}

		this.inProgress = true;
		this.abortController = new AbortController();
		this.sendMessage({ type: 'showInProgress', inProgress: this.inProgress, showStopButton: this.useGpt3 });
		this.currentMessageId = this.getRandomId();

		this.sendMessage({ type: 'addQuestion', value: prompt, code: options.code, autoScroll: this.autoScroll });

		try {
			if (this.useGpt3) {
				if (this.isGpt35Model && this.apiGpt35) {
					const gpt3Response = await this.apiGpt35.sendMessage(question, {
						systemMessage: this.systemContext,
						messageId: this.conversationId,
						parentMessageId: this.messageId,
						abortSignal: this.abortController.signal,
						onProgress: (partialResponse) => {
							this.response = partialResponse.text;
							this.sendMessage({ type: 'addResponse', value: this.response, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });
						},
					});
					({ text: this.response, id: this.conversationId, parentMessageId: this.messageId } = gpt3Response);
				} else if (!this.isGpt35Model && this.apiGpt3) {
					({ text: this.response, conversationId: this.conversationId, parentMessageId: this.messageId } = await this.apiGpt3.sendMessage(question, {
						promptPrefix: this.systemContext,
						abortSignal: this.abortController.signal,
						onProgress: (partialResponse) => {
							this.response = partialResponse.text;
							this.sendMessage({ type: 'addResponse', value: this.response, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });
						},
					}));
				}
			}

			if (options.previousAnswer !== null) {
				this.response = options.previousAnswer + this.response;
			}

			const hasContinuation = ((this.response.split("```").length) % 2) === 0;

			if (hasContinuation) {
				this.response = this.response + " \r\n ```\r\n";
				vscode.window.showInformationMessage("It looks like ChatGPT didn't complete their answer for your coding question. You can ask it to continue and combine the answers.", "Continue and combine answers")
					.then(async (choice) => {
						if (choice === "Continue and combine answers") {
							this.sendApiRequest("Continue", { command: options.command, code: undefined, previousAnswer: this.response });
						}
					});
			}

			this.sendMessage({ type: 'addResponse', value: this.response, done: true, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });

			// Handle generated code with live preview
			if (options.command === 'generateComponent' || this.isComponentGenerationRequest(prompt)) {
				await this.handleGeneratedCode(this.response, this.framework || 'react');
			}

			if (this.subscribeToResponse) {
				vscode.window.showInformationMessage("UIOrbit responded to your question.", "Open conversation").then(async () => {
					await vscode.commands.executeCommand('uiorbit.view.focus');
				});
			}
		} catch (error: any) {
			let message;
			let apiMessage = error?.response?.data?.error?.message || error?.tostring?.() || error?.message || error?.name;

			this.logError("api-request-failed");

			if (error?.response?.status || error?.response?.statusText) {
				message = `${error?.response?.status || ""} ${error?.response?.statusText || ""}`;

				vscode.window.showErrorMessage("An error occurred. If this is due to max_token you could try `UIOrbit: Clear Conversation` command and retry sending your prompt.", "Clear conversation and retry").then(async choice => {
					if (choice === "Clear conversation and retry") {
						await vscode.commands.executeCommand("uiorbit.clearConversation");
						await delay(250);
						this.sendApiRequest(prompt, { command: options.command, code: options.code });
					}
				});
			} else if (error.statusCode === 400) {
				message = `Your method: '${this.loginMethod}' and your model: '${this.model}' may be incompatible or one of your parameters is unknown. Reset your settings to default. (HTTP 400 Bad Request)`;

			} else if (error.statusCode === 401) {
				message = 'Make sure you are properly signed in. If you are using Browser Auto-login method, make sure the browser is open (You could refresh the browser tab manually if you face any issues, too). If you stored your API key in settings.json, make sure it is accurate. If you stored API key in session, you can reset it with `ChatGPT: Reset session` command. (HTTP 401 Unauthorized) Potential reasons: \r\n- 1.Invalid Authentication\r\n- 2.Incorrect API key provided.\r\n- 3.Incorrect Organization provided. \r\n See https://platform.openai.com/docs/guides/error-codes for more details.';
			} else if (error.statusCode === 403) {
				message = 'Your token has expired. Please try authenticating again. (HTTP 403 Forbidden)';
			} else if (error.statusCode === 404) {
				message = `Your method: '${this.loginMethod}' and your model: '${this.model}' may be incompatible or you may have exhausted your ChatGPT subscription allowance. (HTTP 404 Not Found)`;
			} else if (error.statusCode === 429) {
				message = "Too many requests try again later. (HTTP 429 Too Many Requests) Potential reasons: \r\n 1. You exceeded your current quota, please check your plan and billing details\r\n 2. You are sending requests too quickly \r\n 3. The engine is currently overloaded, please try again later. \r\n See https://platform.openai.com/docs/guides/error-codes for more details.";
			} else if (error.statusCode === 500) {
				message = "The server had an error while processing your request, please try again. (HTTP 500 Internal Server Error)\r\n See https://platform.openai.com/docs/guides/error-codes for more details.";
			}

			if (apiMessage) {
				message = `${message ? message + " " : ""}

	${apiMessage}
`;
			}

			this.sendMessage({ type: 'addError', value: message, autoScroll: this.autoScroll });

			return;
		} finally {
			this.inProgress = false;
			this.sendMessage({ type: 'showInProgress', inProgress: this.inProgress });
		}
	}

	/**
	 * Check if the prompt is requesting component generation
	 */
	private isComponentGenerationRequest(prompt: string): boolean {
		const componentKeywords = [
			'component', 'create', 'generate', 'build', 'make',
			'button', 'form', 'card', 'modal', 'navbar', 'sidebar',
			'dashboard', 'page', 'layout', 'ui', 'interface'
		];

		const lowerPrompt = prompt.toLowerCase();
		return componentKeywords.some(keyword => lowerPrompt.includes(keyword));
	}

	/**
	 * Extract and save generated component code
	 */
	private async handleGeneratedCode(response: string, framework: string = 'react'): Promise<void> {
		// Extract code blocks from the response
		const codeBlocks = this.extractCodeBlocks(response);

		if (codeBlocks.length > 0) {
			// Show live preview for the first component
			const mainComponent = codeBlocks.find(block =>
				block.language === 'jsx' ||
				block.language === 'tsx' ||
				block.language === 'vue' ||
				block.language === 'javascript' ||
				block.language === 'typescript'
			);

			if (mainComponent) {
				await this.livePreview.showPreview(mainComponent.code, framework);

				// Offer to save the component
				const shouldSave = await vscode.window.showInformationMessage(
					'💾 Save this component to your project?',
					'Save component',
					'Just preview'
				);

				if (shouldSave === 'Save component') {
					await this.saveGeneratedComponent(mainComponent.code, framework);
				}
			}
		}
	}

	/**
	 * Extract code blocks from markdown response
	 */
	private extractCodeBlocks(response: string): Array<{language: string, code: string}> {
		const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
		const blocks: Array<{language: string, code: string}> = [];
		let match;

		while ((match = codeBlockRegex.exec(response)) !== null) {
			blocks.push({
				language: match[1] || 'javascript',
				code: match[2].trim()
			});
		}

		return blocks;
	}

	/**
	 * Save generated component to the project
	 */
	private async saveGeneratedComponent(code: string, framework: string): Promise<void> {
		const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
		if (!workspaceRoot) return;

		const componentName = await vscode.window.showInputBox({
			prompt: 'Enter component name',
			value: 'MyComponent'
		});

		if (componentName) {
			const extension = framework === 'vue' ? '.vue' :
							 framework === 'angular' ? '.component.ts' :
							 '.tsx';

			const filePath = path.join(workspaceRoot, 'src', 'components', `${componentName}${extension}`);

			// Create components directory if it doesn't exist
			const componentsDir = path.dirname(filePath);
			if (!fs.existsSync(componentsDir)) {
				fs.mkdirSync(componentsDir, { recursive: true });
			}

			// Write the component file
			fs.writeFileSync(filePath, code);

			// Open the file
			const document = await vscode.workspace.openTextDocument(filePath);
			await vscode.window.showTextDocument(document);

			vscode.window.showInformationMessage(`✅ Component saved as ${componentName}${extension}`);
		}
	}

	/**
	 * Install a component library
	 */
	async installComponentLibrary(library: 'shadcn' | 'mui' | 'antd' | 'chakra'): Promise<void> {
		const config = {
			name: library,
			framework: this.framework || 'react',
			components: []
		};

		const success = await this.componentLibrary.installLibrary(config as any);
		if (success) {
			this.sendMessage({
				type: 'addResponse',
				value: `✅ ${library} has been installed successfully! You can now use ${library} components in your project.`,
				autoScroll: this.autoScroll
			});
		}
	}

	/**
	 * Generate component from template
	 */
	async generateFromTemplate(): Promise<void> {
		const libraries = ['shadcn', 'mui', 'antd', 'chakra'];
		const selectedLibrary = await vscode.window.showQuickPick(libraries, {
			placeHolder: 'Select a component library'
		});

		if (!selectedLibrary) return;

		const templates = this.componentLibrary.getComponentTemplates(selectedLibrary);
		const templateNames = templates.map(t => `${t.name} - ${t.description}`);

		const selectedTemplate = await vscode.window.showQuickPick(templateNames, {
			placeHolder: 'Select a component template'
		});

		if (!selectedTemplate) return;

		const templateIndex = templateNames.indexOf(selectedTemplate);
		const template = templates[templateIndex];

		const componentName = await vscode.window.showInputBox({
			prompt: 'Enter component name',
			value: 'MyComponent'
		});

		if (!componentName) return;

		const code = await this.componentLibrary.generateComponent(template, componentName);

		// Show the generated code in the chat
		this.sendMessage({
			type: 'addResponse',
			value: `🎨 **Generated ${componentName} using ${selectedLibrary}:**\n\n\`\`\`tsx\n${code}\n\`\`\``,
			autoScroll: this.autoScroll
		});

		// Show live preview
		await this.livePreview.showPreview(code, this.framework || 'react');

		// Offer to save
		const shouldSave = await vscode.window.showInformationMessage(
			'💾 Save this component to your project?',
			'Save component',
			'Just preview'
		);

		if (shouldSave === 'Save component') {
			await this.saveGeneratedComponent(code, this.framework || 'react');
		}
	}

	/**
	 * Start collaboration session
	 */
	async startCollaboration(): Promise<void> {
		const sessionName = await vscode.window.showInputBox({
			prompt: 'Enter collaboration session name',
			value: 'UIOrbit Session'
		});

		if (sessionName) {
			await this.collaborationEngine.startSession(sessionName);
		}
	}

	/**
	 * Show collaboration panel
	 */
	showCollaborationPanel(): void {
		this.collaborationEngine.showCollaborationPanel();
	}

	/**
	 * Show enterprise dashboard
	 */
	showEnterpriseDashboard(): void {
		this.enterpriseManager.showEnterpriseDashboard();
	}

	/**
	 * Check if feature is available for current tier
	 */
	isFeatureAvailable(featureName: string): boolean {
		return this.enterpriseManager.isFeatureAvailable(featureName);
	}

	/**
	 * Track API usage for billing
	 */
	trackAPIUsage(endpoint: string, success: boolean, responseTime: number): void {
		this.enterpriseManager.trackAPIUsage(endpoint, success, responseTime);
	}

	/**
	 * Message sender, stores if a message cannot be delivered
	 * @param message Message to be sent to WebView
	 * @param ignoreMessageIfNullWebView We will ignore the command if webView is null/not-focused
	 */
	public sendMessage(message: any, ignoreMessageIfNullWebView?: boolean) {
		if (this.webView) {
			this.webView?.webview.postMessage(message);
		} else if (!ignoreMessageIfNullWebView) {
			this.leftOverMessage = message;
		}
	}

	private logEvent(eventName: string, properties?: {}): void {
		// You can initialize your telemetry reporter and consume it here - *replaced with console.debug to prevent unwanted telemetry logs
		// this.reporter?.sendTelemetryEvent(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown", ...properties }, { "uiorbit.questionCounter": this.questionCounter });
		console.debug(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown", ...properties }, { "uiorbit.questionCounter": this.questionCounter });
	}

	private logError(eventName: string): void {
		// You can initialize your telemetry reporter and consume it here - *replaced with console.error to prevent unwanted telemetry logs
		// this.reporter?.sendTelemetryErrorEvent(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown" }, { "uiorbit.questionCounter": this.questionCounter });
		console.error(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown" }, { "uiorbit.questionCounter": this.questionCounter });
	}

	private getWebviewHtml(webview: vscode.Webview) {
		const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'main.js'));
		const stylesMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'main.css'));

		const vendorHighlightCss = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'highlight.min.css'));
		const vendorHighlightJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'highlight.min.js'));
		const vendorMarkedJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'marked.min.js'));
		const vendorTailwindJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'tailwindcss.3.2.4.min.js'));
		const vendorTurndownJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'turndown.js'));

		const nonce = this.getRandomId();

		return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">

				<link href="${stylesMainUri}" rel="stylesheet">
				<link href="${vendorHighlightCss}" rel="stylesheet">
				<script src="${vendorHighlightJs}"></script>
				<script src="${vendorMarkedJs}"></script>
				<script src="${vendorTailwindJs}"></script>
				<script src="${vendorTurndownJs}"></script>
			</head>
			<body class="overflow-hidden">
				<div class="flex flex-col h-screen">
					<div id="introduction" class="flex flex-col justify-between h-full justify-center px-6 w-full relative login-screen overflow-auto">
						<div class="flex items-start text-center features-block my-5">
							<div class="flex flex-col gap-3.5 flex-1">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" class="w-8 h-8 m-auto text-blue-500">
									<path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.994 15.994 0 011.622-3.395m3.42 3.42a15.995 15.995 0 004.764-4.648l3.876-5.814a1.151 1.151 0 00-1.597-1.597L14.146 6.32a15.996 15.996 0 00-4.649 4.763m3.42 3.42a6.776 6.776 0 00-3.42-3.42" />
								</svg>
								<h2 class="text-lg font-semibold text-blue-600">UIOrbit Features</h2>
								<ul class="flex flex-col gap-3.5 text-xs">
									<li class="features-li w-full border border-blue-200 bg-blue-50 p-3 rounded-md">🎨 Generate modern UI components</li>
									<li class="features-li w-full border border-blue-200 bg-blue-50 p-3 rounded-md">📱 Make components responsive & accessible</li>
									<li class="features-li w-full border border-blue-200 bg-blue-50 p-3 rounded-md">🔥 Apply trending UI patterns & styles</li>
									<li class="features-li w-full border border-blue-200 bg-blue-50 p-3 rounded-md">⚡ Context-aware design suggestions</li>
								</ul>
							</div>
						</div>
						<div class="flex flex-col gap-4 h-full items-center justify-end text-center">
							<button id="login-button" class="mb-4 btn btn-primary bg-blue-600 hover:bg-blue-700 text-white flex gap-2 justify-center p-3 rounded-md transition-colors">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
									<path stroke-linecap="round" stroke-linejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
								</svg>
								Connect API Key
							</button>
							<p class="max-w-sm text-center text-xs text-slate-500">
								<a title="Configure UIOrbit settings" id="settings-button" href="#" class="text-blue-600 hover:text-blue-800">⚙️ Settings</a>&nbsp; | &nbsp;<a title="Customize UI prompts" id="settings-prompt-button" href="#" class="text-blue-600 hover:text-blue-800">✨ Prompts</a>
							</p>
							<p class="max-w-sm text-center text-xs text-slate-400 mt-2">
								🚀 Start generating amazing UI components with AI assistance
							</p>
						</div>
					</div>

					<div class="flex-1 overflow-y-auto" id="qa-list"></div>

					<div class="flex-1 overflow-y-auto hidden" id="conversation-list"></div>

					<div id="in-progress" class="pl-4 pt-2 flex items-center hidden">
						<div class="typing">Thinking</div>
						<div class="spinner">
							<div class="bounce1"></div>
							<div class="bounce2"></div>
							<div class="bounce3"></div>
						</div>

						<button id="stop-button" class="btn btn-primary flex items-end p-1 pr-2 rounded-md ml-5">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>Stop responding</button>
					</div>

					<div class="p-4 flex items-center pt-2">
						<div class="flex-1 textarea-wrapper">
							<textarea
								type="text"
								rows="1"
								id="question-input"
								placeholder="Describe the UI component you want to create..."
								onInput="this.parentNode.dataset.replicatedValue = this.value"></textarea>
						</div>
						<div id="chat-button-wrapper" class="absolute bottom-14 items-center more-menu right-8 border border-gray-200 shadow-xl hidden text-xs">
							<button class="flex gap-2 items-center justify-start p-2 w-full" id="clear-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>&nbsp;New chat</button>	
							<button class="flex gap-2 items-center justify-start p-2 w-full" id="list-conversations-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" /></svg>&nbsp;Show conversations</button>
							<button class="flex gap-2 items-center justify-start p-2 w-full" id="settings-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>&nbsp;Update settings</button>
							<button class="flex gap-2 items-center justify-start p-2 w-full" id="export-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" /></svg>&nbsp;Export to markdown</button>
						</div>
						<div id="question-input-buttons" class="right-6 absolute p-0.5 ml-5 flex items-center gap-2">
							<button id="more-button" title="More actions" class="rounded-lg p-0.5">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z" /></svg>
							</button>

							<button id="ask-button" title="Submit prompt" class="ask-button rounded-lg p-0.5">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" /></svg>
							</button>
						</div>
					</div>
				</div>

				<script nonce="${nonce}" src="${scriptUri}"></script>
			</body>
			</html>`;
	}

	private getRandomId() {
		let text = '';
		const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		for (let i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	public dispose(): void {
		// Clean up UIOrbit services
		if (this.fileWatcher) {
			this.fileWatcher.dispose();
		}

		// Clean up vector database
		if (this.vectorDb) {
			this.vectorDb.cleanup();
		}
	}
}
